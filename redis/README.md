# Redis 生产环境配置

## 概述

本配置专为 MoodPlay 生产环境设计，针对音频数据缓存进行了优化。主要特点：

- **内存限制**: 2GB，在资源约束下优化大音频文件缓存
- **持久化**: 已禁用，最大化性能和最小化磁盘使用
- **性能优化**: 6线程I/O，内存碎片整理，无磁盘I/O
- **安全性**: 禁用危险命令，启用保护模式
- **监控**: 完整的监控和管理工具

## 配置特点

### 内存管理
- 最大内存: 2GB (在资源约束下适应1-5MB音频文件)
- 淘汰策略: volatile-lru (仅淘汰有TTL的键)
- 内存碎片整理: 启用，针对大文件优化
- 懒惰释放: 启用，提高大对象删除性能

### 持久化策略
- **完全禁用持久化**:
  - 无RDB快照
  - 无AOF日志
  - 最大化性能，最小化磁盘使用
  - 数据可接受丢失，重启后自动重建

### 性能优化
- I/O线程: 6个线程 (针对大文件)
- 读操作多线程: 启用
- 慢查询监控: 10ms阈值
- 客户端缓冲区: 64MB，支持5MB音频文件
- 过期清理: 更频繁 (hz=20)

### 安全设置
- 保护模式: 启用
- 危险命令重命名或禁用:
  - FLUSHDB, FLUSHALL, KEYS, CONFIG, DEBUG: 禁用
  - SHUTDOWN: 重命名为 REDIS_SHUTDOWN_CMD

## 部署说明

### 1. 启动生产环境Redis
```bash
# 使用新配置启动
docker-compose -f docker-compose.prod.yml up -d redis

# 或使用管理脚本
./redis/prod/redis-prod-utils.sh start
```

### 2. 验证配置
```bash
# 检查Redis状态
./redis/prod/redis-prod-utils.sh status

# 查看配置
./redis/prod/redis-prod-utils.sh config

# 检查内存设置
./redis/prod/redis-prod-utils.sh memory
```

### 3. 健康检查
```bash
# Docker会自动进行健康检查
docker ps  # 查看容器状态

# 手动健康检查
docker exec redis redis-cli ping
```

## 管理工具使用

### 基本操作
```bash
# 启动Redis
./redis/prod/redis-prod-utils.sh start

# 停止Redis
./redis/prod/redis-prod-utils.sh stop

# 重启Redis
./redis/prod/redis-prod-utils.sh restart

# 连接Redis CLI
./redis/prod/redis-prod-utils.sh connect
```

### 监控命令
```bash
# 查看内存使用
./redis/prod/redis-prod-utils.sh memory

# 查看统计信息
./redis/prod/redis-prod-utils.sh stats

# 实时监控
./redis/prod/redis-prod-utils.sh monitor

# 检查音频缓存
./redis/prod/redis-prod-utils.sh audio
```

### 备份操作
```bash
# 创建备份
./redis/prod/redis-prod-utils.sh backup

# 备份文件位置
ls -la ./redis/backups/
```

## 音频缓存优化

### 缓存策略
- **TTL**: 15分钟 (900秒)
- **数据格式**: Base64编码的音频数据
- **键模式**: 
  - `empathy_audio:{playSessionId}`
  - `workflow_audio:{playSessionId}`

### 预期性能
- **存储容量**: 约400-2000个音频文件
- **单文件大小**: 1-5MB
- **访问模式**: 低读取频率，写入一次读取少量

### 内存使用估算
```
音频文件大小范围: 1-5MB
平均文件大小: ~3MB
2GB内存可存储: ~650个音频文件
考虑Redis开销: ~500个音频文件
峰值容量: 可支持约2000个1MB文件或400个5MB文件
注意: 资源受限，需要更频繁的缓存淘汰
```

## 监控和告警

详细的监控指南请参考: [redis-monitoring.md](./redis-monitoring.md)

### 关键指标
- 内存使用率 < 90% (1.8GB)
- 缓存命中率 > 75% (低频读取+资源受限场景)
- 平均延迟 < 2ms (大文件处理)
- 连接数 < 8000
- 淘汰频率监控 (资源受限需要关注)

### 日常检查
```bash
# 每日检查脚本
#!/bin/bash
echo "=== Redis Daily Check ==="
./redis/prod/redis-prod-utils.sh status
./redis/prod/redis-prod-utils.sh memory
./redis/prod/redis-prod-utils.sh audio
```

## 故障排查

### 常见问题

1. **内存不足**
   - 检查: `./redis/prod/redis-prod-utils.sh memory`
   - 解决: 清理过期缓存或增加内存

2. **性能下降**
   - 检查: `./redis/prod/redis-prod-utils.sh stats`
   - 解决: 查看慢查询日志，优化查询

3. **连接失败**
   - 检查: `./redis/prod/redis-prod-utils.sh status`
   - 解决: 重启Redis服务

### 紧急恢复
```bash
# 如果Redis无法启动
docker-compose -f docker-compose.prod.yml logs redis

# 重置Redis数据 (谨慎使用)
docker-compose -f docker-compose.prod.yml down
docker volume rm moodplay_redis_data
docker-compose -f docker-compose.prod.yml up -d redis
```

## 升级和维护

### 配置更新
1. 修改 `redis.conf`
2. 重启Redis: `./redis/prod/redis-prod-utils.sh restart`
3. 验证配置: `./redis/prod/redis-prod-utils.sh config`

### 版本升级
1. 备份数据: `./redis/prod/redis-prod-utils.sh backup`
2. 更新Docker镜像版本
3. 重新部署
4. 验证功能

### 定期维护
- 每周创建备份
- 每月检查性能指标
- 每季度评估配置优化

## 联系和支持

如有问题，请检查：
1. Redis日志: `docker-compose -f docker-compose.prod.yml logs redis`
2. 监控指标: `./redis/prod/redis-prod-utils.sh stats`
3. 配置文件: `./redis/prod/redis.conf`
