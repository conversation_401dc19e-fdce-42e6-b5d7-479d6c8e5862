# Redis Configuration for Production Environment
# Optimized for audio caching and general production workloads
# Memory limit: 2GB for audio data caching

# ========================================================================================
# NETWORK AND BASIC SETTINGS
# ========================================================================================

# Accept connections from any IP (safe in containerized production environment)
bind 0.0.0.0

# Default port
port 6379

# Enable protected mode for security
protected-mode yes

# TCP listen backlog
tcp-backlog 511

# Close connection after client is idle for N seconds
timeout 300

# TCP keepalive
tcp-keepalive 300

# ========================================================================================
# MEMORY MANAGEMENT
# ========================================================================================

# Set maximum memory limit for production environment (2GB for large audio caching)
# Optimized for 1-5MB audio files with limited server resources
maxmemory 2gb

# Memory eviction policy - use volatile-lru for TTL-based cache
# This only evicts keys with TTL set, perfect for audio cache with 15min TTL
maxmemory-policy volatile-lru

# Increase samples for better LRU accuracy with large files
maxmemory-samples 10

# ========================================================================================
# PERSISTENCE SETTINGS - DISABLED FOR PERFORMANCE
# ========================================================================================

# Disable all persistence for maximum performance and minimal disk usage
# Audio cache data is acceptable to lose and will be regenerated as needed

# Disable RDB snapshots completely
save ""

# Disable AOF (Append Only File) logging
appendonly no

# Keep these settings for potential future use, but disabled
# dbfilename dump.rdb
# dir /data
# rdbcompression no
# rdbchecksum no
# appendfilename "appendonly.aof"
# appendfsync no

# ========================================================================================
# LOGGING
# ========================================================================================

# Log level for production environment
loglevel notice

# Log to stdout (will be captured by Docker)
logfile ""

# ========================================================================================
# CLIENT SETTINGS
# ========================================================================================

# Maximum number of connected clients
maxclients 10000

# ========================================================================================
# PERFORMANCE OPTIMIZATIONS FOR PRODUCTION
# ========================================================================================

# Enable slow log for monitoring
slowlog-log-slower-than 10000
slowlog-max-len 128

# Hash table rehashing
hash-max-ziplist-entries 512
hash-max-ziplist-value 64

# List compression
list-max-ziplist-size -2
list-compress-depth 0

# Set compression
set-max-intset-entries 512

# Sorted set compression
zset-max-ziplist-entries 128
zset-max-ziplist-value 64

# HyperLogLog sparse representation
hll-sparse-max-bytes 3000

# ========================================================================================
# SECURITY SETTINGS
# ========================================================================================

# Disable dangerous commands in production
rename-command FLUSHDB ""
rename-command FLUSHALL ""
rename-command KEYS ""
rename-command CONFIG ""
rename-command SHUTDOWN REDIS_SHUTDOWN_CMD
rename-command DEBUG ""

# ========================================================================================
# ADVANCED SETTINGS
# ========================================================================================

# Enable automatic memory defragmentation for large file handling
activedefrag yes

# Defragmentation thresholds - more aggressive for large audio files
active-defrag-ignore-bytes 200mb
active-defrag-threshold-lower 5
active-defrag-threshold-upper 50
active-defrag-cycle-min 1
active-defrag-cycle-max 25

# Enable lazy freeing for better performance with large objects
lazyfree-lazy-eviction yes
lazyfree-lazy-expire yes
lazyfree-lazy-server-del yes

# Increased client output buffer limits for large audio files (1-5MB)
client-output-buffer-limit normal 64mb 32mb 60
client-output-buffer-limit replica 512mb 128mb 60
client-output-buffer-limit pubsub 64mb 16mb 60

# Frequency of background tasks
hz 10

# ========================================================================================
# REPLICATION SETTINGS
# ========================================================================================

# Replica settings (for future scaling)
replica-read-only yes
replica-serve-stale-data yes

# ========================================================================================
# MEMORY USAGE REPORTING
# ========================================================================================

# Memory usage tracking is available via INFO memory command

# ========================================================================================
# PRODUCTION OPTIMIZATIONS
# ========================================================================================

# Enable multi-threading for I/O operations (Redis 6+)
# Increased threads for better large file handling
io-threads 6
io-threads-do-reads yes

# Optimize for large audio caching workload
# These settings are tuned for storing 1-5MB base64 audio data with 15-minute TTL

# ========================================================================================
# EXPIRATION AND CLEANUP - OPTIMIZED FOR TTL-BASED CACHE
# ========================================================================================

# Active expiration settings - more aggressive for TTL-heavy workload
# Check more keys per second for expiration since we rely heavily on TTL
hz 20

# Increase CPU effort for expiring keys since we don't persist data
active-expire-effort 3

# ========================================================================================
# MODULES AND EXTENSIONS
# ========================================================================================

# No additional modules needed for production environment

# ========================================================================================
# MONITORING AND DEBUGGING
# ========================================================================================

# Enable latency monitoring
latency-monitor-threshold 100

# ========================================================================================
# LARGE AUDIO CACHING SPECIFIC OPTIMIZATIONS
# ========================================================================================

# Optimized for large base64 audio data (1-5MB per file):
# - 4GB memory limit for larger capacity
# - volatile-lru eviction (only evicts TTL keys)
# - Disabled persistence for maximum performance
# - Increased I/O threads and buffer limits
# - Aggressive memory defragmentation

# Expected usage pattern:
# - Audio files: 1-5MB each, cached for 15 minutes (900 seconds)
# - Low read frequency per individual file
# - Write-once, read-few pattern
# - Automatic cleanup via TTL (no persistence needed)

# Memory allocation:
# - 2GB total memory limit (resource-constrained environment)
# - Approximately 400-2000 audio files can be cached simultaneously
# - volatile-lru ensures only TTL keys are evicted, preserving other cache data

# Performance considerations:
# - No disk I/O for persistence = maximum performance
# - 6 I/O threads for large file handling
# - Optimized buffer limits for 5MB files within 2GB constraint
# - Aggressive expiration cleanup (hz=20, effort=3)
# - Memory defragmentation optimized for large objects

# Cost considerations:
# - No persistent storage usage
# - Limited memory allocation for cost control
# - Optimized for acceptable data loss scenario
